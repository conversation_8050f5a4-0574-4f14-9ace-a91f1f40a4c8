import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertSongOrderSchema, type InsertSongOrder } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import PayPalButton from "@/components/PayPalButton";
import logoPath from "@assets/Dynamic Logo for KMStudio PH - Music and Creative Studio.png";
import { 
  Music, 
  Youtube, 
  Facebook, 
  Mail, 
  Phone, 
  MapPin, 
  Check, 
  Heart, 
  Cross, 
  Download,
  Send,
  QrCode,
  CreditCard,
  X,
  Upload,
  Menu,
  Egg,
  Star,
  Gift,
  Calendar,
  Cake,
  GraduationCap,
  Sparkles
} from "lucide-react";
import { Link } from "wouter";

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [gcashModalOpen, setGcashModalOpen] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<string>("");
  const { toast } = useToast();

  const form = useForm<InsertSongOrder>({
    resolver: zodResolver(insertSongOrderSchema),
    defaultValues: {
      fullName: "",
      email: "",
      songTitle: "",
      occasion: "",
      mood: "",
      lyrics: "",
      referenceFile: "",
    },
  });

  const createOrderMutation = useMutation({
    mutationFn: async (data: InsertSongOrder) => {
      const response = await apiRequest("POST", "/api/song-orders", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Request Submitted Successfully!",
        description: "We'll review your request and contact you within 24 hours to discuss your custom song.",
      });
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your request. Please try again or contact us directly.",
        variant: "destructive",
      });
      console.error("Error:", error);
    },
  });

  const onSubmit = (data: InsertSongOrder) => {
    createOrderMutation.mutate(data);
  };

  const openPaymentModal = (packageType: string) => {
    setSelectedPackage(packageType);
    setPaymentModalOpen(true);
  };

  const closePaymentModal = () => {
    setPaymentModalOpen(false);
    setSelectedPackage("");
  };

  const showGCashQR = () => {
    closePaymentModal();
    setGcashModalOpen(true);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      setMobileMenuOpen(false);
    }
  };

  const packages = {
    basic: { price: "₱1,000.00", name: "Basic" },
    standard: { price: "₱2,000", name: "Standard" },
    premium: { price: "₱3,500", name: "Premium" }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav role="navigation" aria-label="Main navigation" className="fixed top-0 w-full z-50 bg-background/90 backdrop-blur-md border-b border-border transition-all duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14 md:h-16">
            <div className="flex items-center space-x-2 md:space-x-3">
              <img src={logoPath} alt="KMStudioPH - Custom song creation and inspirational music logo" className="h-8 md:h-10 w-auto transition-all duration-300" />
              <span className="font-bold text-lg md:text-xl text-foreground">KMStudioPH</span>
            </div>
            
            <div className="hidden md:flex space-x-6 lg:space-x-8" role="menubar">
              <button onClick={() => scrollToSection('home')} className="text-muted-foreground hover:text-primary transition-all duration-300 py-2 px-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded" role="menuitem" aria-label="Navigate to home section">Home</button>
              <button onClick={() => scrollToSection('songs')} className="text-muted-foreground hover:text-primary transition-all duration-300 py-2 px-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded" role="menuitem" aria-label="Navigate to published songs section">Songs</button>
              <button onClick={() => scrollToSection('order')} className="text-muted-foreground hover:text-primary transition-all duration-300 py-2 px-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded" role="menuitem" aria-label="Navigate to order form section">Order</button>
              <button onClick={() => scrollToSection('pricing')} className="text-muted-foreground hover:text-primary transition-all duration-300 py-2 px-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded" role="menuitem" aria-label="Navigate to pricing packages section">Pricing</button>
              <button onClick={() => scrollToSection('contact')} className="text-muted-foreground hover:text-primary transition-all duration-300 py-2 px-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded" role="menuitem" aria-label="Navigate to contact information section">Contact</button>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2 hover-lift focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label={mobileMenuOpen ? "Close mobile navigation menu" : "Open mobile navigation menu"}
              aria-expanded={mobileMenuOpen}
              aria-controls="mobile-menu"
            >
              <Menu className="h-5 w-5 md:h-6 md:w-6" aria-hidden="true" />
            </Button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div id="mobile-menu" className="md:hidden bg-white border-t border-gray-200 animate-fade-in-up" role="menu" aria-label="Mobile navigation menu">
            <div className="px-4 py-4 space-y-1">
              <button onClick={() => scrollToSection('home')} className="block w-full text-left text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-300 py-3 px-2 rounded-lg mobile-btn focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" role="menuitem" aria-label="Navigate to home section">Home</button>
              <button onClick={() => scrollToSection('songs')} className="block w-full text-left text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-300 py-3 px-2 rounded-lg mobile-btn focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" role="menuitem" aria-label="Navigate to published songs section">Songs</button>
              <button onClick={() => scrollToSection('order')} className="block w-full text-left text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-300 py-3 px-2 rounded-lg mobile-btn focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" role="menuitem" aria-label="Navigate to order form section">Order</button>
              <button onClick={() => scrollToSection('pricing')} className="block w-full text-left text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-300 py-3 px-2 rounded-lg mobile-btn focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" role="menuitem" aria-label="Navigate to pricing packages section">Pricing</button>
              <button onClick={() => scrollToSection('contact')} className="block w-full text-left text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-300 py-3 px-2 rounded-lg mobile-btn focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" role="menuitem" aria-label="Navigate to contact information section">Contact</button>
            </div>
          </div>
        )}
      </nav>
      {/* Hero Section */}
      <section id="home" className="min-h-screen flex items-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-700 to-cyan-500"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&h=1380')"
          }}
        ></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-bounce mb-8">
            <Music className="h-16 w-16 sm:h-24 sm:w-24 text-white mx-auto opacity-90" />
          </div>
          
          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
            KMStudioPH
          </h1>
          
          <p className="text-xl sm:text-2xl lg:text-3xl text-white/90 mb-8 font-light">
            Personalized Songs. Inspirational Soundtracks of Faith.
          </p>
          
          <p className="text-lg sm:text-xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
            Creating beautiful, meaningful music that touches hearts and souls. From custom wedding songs to inspirational worship music, we bring your stories to life through melody and faith.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              variant="outline" 
              size="lg"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
              onClick={() => window.open('https://youtube.com/@kmstudioph', '_blank')}
            >
              <Youtube className="mr-2 h-5 w-5" />
              Subscribe on YouTube
            </Button>
            
            <Button 
              variant="outline" 
              size="lg"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
              onClick={() => window.open('https://facebook.com/kmstudioph', '_blank')}
            >
              <Facebook className="mr-2 h-5 w-5" />
              Like on Facebook
            </Button>
          </div>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-20 left-10 text-white/30 animate-pulse">
          <Cross className="h-8 w-8" />
        </div>
        <div className="absolute bottom-20 right-10 text-white/30 animate-pulse" style={{ animationDelay: '1s' }}>
          <Egg className="h-6 w-6" />
        </div>
        <div className="absolute top-40 right-20 text-white/30 animate-pulse" style={{ animationDelay: '2s' }}>
          <Heart className="h-6 w-6" />
        </div>
      </section>



      {/* Published Songs Section */}
      <section id="songs" className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">Our Published Songs</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Listen to our collection of inspirational songs that have touched hearts and strengthened faith across the Philippines.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Featured Song - Timmy & Teddy */}
            <Card className="bg-gradient-to-br from-yellow-50 to-amber-50 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-2 border-yellow-200">
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <video 
                  controls 
                  className="w-full h-full object-cover"
                  poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgdmlld0JveD0iMCAwIDgwMCA2MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI4MDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjRkZGN0VEIi8+CjxwYXRoIGQ9Ik00MDAgMjgwQzQyMi4wOTEgMjgwIDQ0MCAyOTcuOTA5IDQ0MCAzMjBDNDQwIDM0Mi4wOTEgNDIyLjA5MSAzNjAgNDAwIDM2MEM0MzcuOTA5IDM2MCAzNjAgMzQyLjA5MSAzNjAgMzIwQzM2MCAyOTcuOTA5IDM3Ny45MDkgMjgwIDQwMCAyODBaIiBmaWxsPSIjRkJCRjI0Ii8+CjwvZz4KPC9zdmc+"
                >
                  <source src="@assets/Timmy & Teddy (You're my Always).mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-semibold">FEATURED</span>
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">NEW</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Timmy & Teddy (You're my Always)</h3>
                <p className="text-gray-600 mb-4">A heartwarming custom song celebrating a mother's unconditional love for her twin boys</p>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-primary font-medium">Family Love Song • Premium</span>
                  <Button variant="ghost" size="sm">
                    <Heart className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Song Card 2 */}
            <Card className="bg-gradient-to-br from-blue-50 to-white hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <img 
                  src="https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600" 
                  alt="Beautiful wedding ceremony moment" 
                  className="w-full h-full object-cover" 
                />
              </div>
              
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Forever Yours</h3>
                <p className="text-gray-600 mb-4">A romantic wedding song celebrating love blessed by God, based on 1 Corinthians 13</p>
                
                <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-xl p-4 mb-4">
                  <audio controls className="w-full">
                    <source src="#" type="audio/mpeg" />
                    Your browser does not support the audio element.
                  </audio>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-primary font-medium">Wedding • 4:15</span>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Song Card 3 */}
            <Card className="bg-gradient-to-br from-blue-50 to-white hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                <img 
                  src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600" 
                  alt="Joyful birthday celebration with family" 
                  className="w-full h-full object-cover" 
                />
              </div>
              
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Blessed Another Year</h3>
                <p className="text-gray-600 mb-4">A joyful birthday song thanking God for His blessings and faithfulness through the years</p>
                
                <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-xl p-4 mb-4">
                  <audio controls className="w-full">
                    <source src="#" type="audio/mpeg" />
                    Your browser does not support the audio element.
                  </audio>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-primary font-medium">Birthday • 2:58</span>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>


      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Getting your custom song is simple! Follow these easy steps to bring your musical vision to life.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-8 mb-20">
            {/* Step 1 */}
            <div className="text-center group">
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary to-amber-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300 animate-pulse">
                  1
                </div>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-8 border-transparent border-t-primary opacity-50"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Fill Out Form</h3>
              <p className="text-gray-600">Share your story, occasion, and song preferences with us</p>
            </div>
            
            {/* Step 2 */}
            <div className="text-center group">
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary to-amber-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300" style={{ animationDelay: '0.5s' }}>
                  2
                </div>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-8 border-transparent border-t-primary opacity-50"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Choose Package</h3>
              <p className="text-gray-600">Select from Basic (₱1,000), Standard (₱2,000), or Premium (₱3,500)</p>
            </div>
            
            {/* Step 3 */}
            <div className="text-center group">
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary to-amber-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300" style={{ animationDelay: '1s' }}>
                  3
                </div>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-8 border-transparent border-t-primary opacity-50"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Secure Payment</h3>
              <p className="text-gray-600">Pay safely through PayPal with full transaction protection</p>
            </div>
            
            {/* Step 4 */}
            <div className="text-center group">
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary to-amber-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300" style={{ animationDelay: '1.5s' }}>
                  4
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Get Your Song</h3>
              <p className="text-gray-600">We'll contact you within 24 hours and deliver your custom song</p>
            </div>
          </div>
          
          {/* Animated Process Flow */}
          <div className="relative mb-16">
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-primary/20 via-primary to-primary/20 transform -translate-y-1/2"></div>
            <div className="relative flex justify-between items-center">
              <div className="w-4 h-4 bg-primary rounded-full animate-bounce"></div>
              <div className="w-4 h-4 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.5s' }}></div>
              <div className="w-4 h-4 bg-primary rounded-full animate-bounce" style={{ animationDelay: '1s' }}></div>
              <div className="w-4 h-4 bg-primary rounded-full animate-bounce" style={{ animationDelay: '1.5s' }}></div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Song Order Section */}
      <section id="order" className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">Order Your Custom Song</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Ready to get started? Fill out the form below and let's create something beautiful together!
            </p>
          </div>
          
          <Card className="shadow-2xl">
            <CardContent className="p-8 lg:p-12">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-900">Full Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your full name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-900">Email Address *</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="songTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-gray-900">Requested Song Title</FormLabel>
                        <FormControl>
                          <Input placeholder="My Song Title (Optional)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="occasion"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-900">Occasion *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an occasion" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Wedding">Wedding</SelectItem>
                              <SelectItem value="Birthday">Birthday</SelectItem>
                              <SelectItem value="Worship">Worship</SelectItem>
                              <SelectItem value="Anniversary">Anniversary</SelectItem>
                              <SelectItem value="Graduation">Graduation</SelectItem>
                              <SelectItem value="Memorial">Memorial</SelectItem>
                              <SelectItem value="Christmas">Christmas</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="mood"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-900">Preferred Mood/Genre</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select mood/genre" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Uplifting/Joyful">Uplifting/Joyful</SelectItem>
                              <SelectItem value="Romantic/Tender">Romantic/Tender</SelectItem>
                              <SelectItem value="Peaceful/Meditative">Peaceful/Meditative</SelectItem>
                              <SelectItem value="Celebratory">Celebratory</SelectItem>
                              <SelectItem value="Reflective">Reflective</SelectItem>
                              <SelectItem value="Contemporary Worship">Contemporary Worship</SelectItem>
                              <SelectItem value="Traditional Hymn Style">Traditional Hymn Style</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="lyrics"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-gray-900">Sample Lyrics or Message *</FormLabel>
                        <FormControl>
                          <Textarea 
                            rows={5}
                            placeholder="Share your story, key phrases, Bible verses, or any specific lyrics you'd like included. The more details you provide, the more personalized your song will be." 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2">Upload Reference File</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-primary transition-colors">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">Click to upload audio sample, lyrics document, or reference material</p>
                      <p className="text-sm text-gray-500 mt-1">Supports MP3, WAV, DOC, PDF, TXT files</p>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <Button 
                      type="submit" 
                      size="lg"
                      disabled={createOrderMutation.isPending}
                      className="px-12 py-4 text-lg"
                    >
                      {createOrderMutation.isPending ? (
                        <LoadingSpinner className="mr-2" />
                      ) : (
                        <Send className="mr-2 h-5 w-5" />
                      )}
                      Submit Song Request
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </section>
      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">Pricing Packages</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
              Choose the perfect package for your custom song needs. All packages include professional recording and mixing.
            </p>
            
            {/* FREE SONGS HIGHLIGHT */}
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6 rounded-2xl max-w-2xl mx-auto mb-8 shadow-lg">
              <div className="text-2xl font-bold mb-2">🎁 SPECIAL OFFER: 2 FREE SONGS! 🎁</div>
              <p className="text-lg">Get your first 2 custom songs absolutely FREE! After that, enjoy our paid packages for unlimited musical creations.</p>
            </div>
            
            {/* AI TRANSPARENCY NOTE */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto">
              <p className="text-sm text-blue-800">
                <span className="font-semibold">🎵 Creative Process:</span> Our songs feature AI-assisted composition with human-written lyrics and professional human creativity oversight for the perfect personal touch.
              </p>
            </div>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Basic Package */}
            <Card className="bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 text-white hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold mb-2">Basic</h3>
                <div className="text-4xl font-bold mb-2">₱1,000.00</div>
                <p className="text-white/90 mb-8">Perfect for simple occasions</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center text-gray-700">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    Chorus only (30-60 seconds)
                  </li>
                  <li className="flex items-center text-gray-700">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    Professional vocals
                  </li>
                  <li className="flex items-center text-gray-700">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    Basic instrumental
                  </li>
                  <li className="flex items-center text-gray-700">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    Digital delivery (MP3)
                  </li>
                  <li className="flex items-center text-gray-700">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    3-day turnaround
                  </li>
                </ul>
                
                <Button 
                  onClick={() => openPaymentModal('basic')}
                  className="w-full"
                >
                  Choose Basic
                </Button>
              </CardContent>
            </Card>
            
            {/* Standard Package */}
            <Card className="bg-gradient-to-br from-purple-600 via-blue-500 to-teal-500 text-white hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-gray-900 px-6 py-2 rounded-full text-sm font-semibold">
                Most Popular
              </div>
              
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold mb-2">Standard</h3>
                <div className="text-4xl font-bold mb-2">₱2,000</div>
                <p className="text-white/90 mb-8">Complete song experience</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    Full song (2-3 minutes)
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    Professional vocals & harmonies
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    Rich instrumental arrangement
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    Professional mixing & mastering
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    High-quality audio (WAV + MP3)
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-amber-400 mr-3" />
                    5-7 day turnaround
                  </li>
                </ul>
                
                <Button 
                  onClick={() => openPaymentModal('standard')}
                  variant="secondary"
                  className="w-full bg-white text-primary hover:bg-gray-100"
                >
                  Choose Standard
                </Button>
              </CardContent>
            </Card>
            
            {/* Premium Package */}
            <Card className="bg-gradient-to-br from-pink-500 via-red-500 to-orange-500 text-white hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold mb-2">Premium</h3>
                <div className="text-4xl font-bold mb-2">₱3,500</div>
                <p className="text-white/90 mb-8">Complete package with extras</p>
                
                <ul className="space-y-4 mb-8 text-left">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Full song (2-3 minutes)
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Professional vocals & harmonies
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Original instrumental track
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Separate instrumental version
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Multiple audio formats
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    Lyric sheet included
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-yellow-300 mr-3" />
                    7-10 day turnaround
                  </li>
                </ul>
                
                <Button 
                  onClick={() => openPaymentModal('premium')}
                  className="w-full bg-white text-pink-600 hover:bg-gray-100 font-semibold"
                >
                  Choose Premium
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
            <p className="text-xl text-gray-600">Real stories from families who've experienced the magic of personalized music</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 hover-lift">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 italic">
                "KMStudioPH created the most beautiful wedding song for us. Every time we hear it, we're transported back to our special day. It captures our love story perfectly!"
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-amber-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  M&J
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Maria & Jose</p>
                  <p className="text-sm text-gray-500">Wedding Song Package</p>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 hover-lift">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 italic">
                "As a mother, hearing my daughter's story turned into such a beautiful melody brought tears to my eyes. This song will be treasured in our family forever."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-amber-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  L
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Liza Santos</p>
                  <p className="text-sm text-gray-500">Family Tribute Song</p>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 hover-lift">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-gray-700 mb-6 italic">
                "The team understood our vision perfectly. They created an inspirational song for our church that touches everyone who hears it. Truly gifted musicians!"
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-amber-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  R
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Pastor Roberto</p>
                  <p className="text-sm text-gray-500">Church Ministry Song</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-primary to-amber-500 hover:from-primary/90 hover:to-amber-500/90"
              onClick={() => scrollToSection('order')}
            >
              Create Your Story in Song
            </Button>
          </div>
        </div>
      </section>

      {/* Affiliate Program Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">🎵 Share the Music, Earn Rewards! 🎵</h2>
            <p className="text-xl text-gray-700">Join our KMStudioPH Affiliate Program and earn money by sharing the gift of music</p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12 items-start">
            <div className="h-full">
              <div className="bg-white rounded-2xl shadow-xl p-8 h-full">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">How Our Affiliate Program Works</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-900 via-rose-600 to-sky-400 rounded-full flex items-center justify-center text-white font-bold mr-4 mt-1">
                      1
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Share Your Unique Link</h4>
                      <p className="text-gray-700">Get your personal referral link and share it with friends, family, and social media</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-900 via-rose-600 to-sky-400 rounded-full flex items-center justify-center text-white font-bold mr-4 mt-1">
                      2
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Friend Orders a Song</h4>
                      <p className="text-gray-700">When someone uses your link to order a custom song, they get a special discount</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-900 via-rose-600 to-sky-400 rounded-full flex items-center justify-center text-white font-bold mr-4 mt-1">
                      3
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">You Earn Commission</h4>
                      <p className="text-gray-700">Receive your affiliate bonus directly to your GCash or bank account</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <div className="bg-gradient-to-r from-purple-900 via-rose-600 to-sky-400 rounded-2xl shadow-xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-6 text-center">💰 Affiliate Rewards</h3>
                
                <div className="space-y-4 mb-8">
                  <div className="bg-white/20 rounded-lg p-4">
                    <div className="text-2xl font-bold">5% Commission</div>
                    <div className="text-primary-foreground/80">On All Package Types</div>
                  </div>
                  
                  <div className="bg-white/20 rounded-lg p-4">
                    <div className="text-2xl font-bold">₱50</div>
                    <div className="text-primary-foreground/80">Basic Package Referral (₱1,000)</div>
                  </div>
                  
                  <div className="bg-white/20 rounded-lg p-4">
                    <div className="text-2xl font-bold">₱100</div>
                    <div className="text-primary-foreground/80">Standard Package Referral (₱2,000)</div>
                  </div>
                  
                  <div className="bg-white/20 rounded-lg p-4">
                    <div className="text-2xl font-bold">₱175</div>
                    <div className="text-primary-foreground/80">Premium Package Referral (₱3,500)</div>
                  </div>
                </div>
                
                <div className="border-t border-white/20 pt-6">
                  <h4 className="font-semibold mb-4 text-center text-lg">🚀 EXPLOSIVE BONUS REWARDS! 🚀</h4>
                  <div className="space-y-3">
                    <div className="bg-white/30 rounded-lg p-3 text-center">
                      <div className="text-2xl font-bold">₱250 BONUS!</div>
                      <div className="text-sm">Hit 5 referrals this month</div>
                      <div className="text-xs opacity-90">💪 YOU CAN DO THIS!</div>
                    </div>
                    <div className="bg-white/30 rounded-lg p-3 text-center">
                      <div className="text-2xl font-bold">₱600 MEGA BONUS!</div>
                      <div className="text-sm">Reach 10 referrals this month</div>
                      <div className="text-xs opacity-90">🔥 ALMOST DOUBLE YOUR EARNINGS!</div>
                    </div>
                    <div className="bg-yellow-400/20 border border-yellow-300/50 rounded-lg p-3 text-center">
                      <div className="text-2xl font-bold text-yellow-100">₱1,500 CHAMPION BONUS!</div>
                      <div className="text-sm">Be the TOP affiliate this month</div>
                      <div className="text-xs opacity-90">👑 BECOME THE AFFILIATE CHAMPION!</div>
                    </div>
                  </div>
                  <div className="mt-4 text-center">
                    <div className="text-sm font-semibold animate-pulse">
                      ⚡ BONUSES STACK WITH YOUR COMMISSIONS! ⚡
                    </div>
                    <div className="text-xs mt-1 opacity-90">
                      Earn up to ₱2,100 EXTRA per month in bonuses alone!
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 text-center space-y-4">
                <div className="relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 rounded-lg blur opacity-75 animate-pulse"></div>
                  <Button 
                    size="lg" 
                    className="relative bg-gradient-to-r from-purple-900 via-rose-600 to-sky-400 hover:from-purple-800 hover:via-rose-700 hover:to-sky-500 text-white font-bold px-8 py-6 text-lg transform hover:scale-105 transition-all duration-200"
                    onClick={() => alert('🎵 Ready to start earning? Contact us NOW!\n\n📱 GCash: 0917-578-1803\n📘 Facebook: KMStudioPH\n\n💰 Start earning ₱50-₱175 per referral + BONUSES!\n🚀 Your affiliate journey begins today!')}
                  >
                    💰 START EARNING TODAY! 💰
                    <div className="text-sm font-normal mt-1">
                      Join 100+ Successful Affiliates!
                    </div>
                  </Button>
                </div>
                
                <div className="bg-gradient-to-r from-red-600 via-pink-600 to-orange-500 rounded-lg p-6 max-w-md mx-auto shadow-2xl border-4 border-yellow-400 transform hover:scale-105 transition-all duration-300">
                  <div className="text-xl font-extrabold mb-3 text-yellow-100 drop-shadow-2xl text-center">🔥 LIMITED TIME: DOUBLE BONUS WEEK! 🔥</div>
                  <div className="text-base text-yellow-50 font-bold drop-shadow-lg text-center">
                    Join this week and get 2X bonus on your first 3 referrals!
                  </div>
                  <div className="text-base mt-2 animate-bounce text-yellow-200 font-black drop-shadow-lg text-center bg-black/20 rounded-full py-1 px-3">
                    ⏰ Hurry! Offer ends soon!
                  </div>
                </div>
                
                <div className="flex gap-2 justify-center flex-wrap">
                  <Link href="/sales-dashboard">
                    <Button variant="outline" size="sm" className="text-purple-700 bg-white/90 border-purple-300 hover:bg-purple-100 font-semibold">
                      📊 Sales Dashboard
                    </Button>
                  </Link>
                  <Link href="/affiliate-dashboard">
                    <Button variant="outline" size="sm" className="text-purple-700 bg-white/90 border-purple-300 hover:bg-purple-100 font-semibold">
                      💼 Affiliate Dashboard
                    </Button>
                  </Link>
                  <Link href="/referral-graphics">
                    <Button variant="outline" size="sm" className="text-purple-700 bg-white/90 border-purple-300 hover:bg-purple-100 font-semibold">
                      🎨 Referral Graphics
                    </Button>
                  </Link>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-bold text-red-600 bg-white/90 px-3 py-1 rounded-lg">
                    🔥 Contact us NOW to get your unique referral link!
                  </p>
                  <div className="flex items-center justify-center space-x-4 text-xs">
                    <div className="flex items-center bg-white/90 px-2 py-1 rounded-lg">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-1"></div>
                      <span className="text-purple-800 font-semibold">127 affiliates active</span>
                    </div>
                    <div className="flex items-center bg-white/90 px-2 py-1 rounded-lg">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse mr-1"></div>
                      <span className="text-purple-800 font-semibold">₱45,000 paid this month</span>
                    </div>
                  </div>
                  <div className="text-xs bg-white/90 px-3 py-1 rounded-lg text-center">
                    <span className="text-purple-700 font-medium">⭐ "I earned ₱2,800 in my first month!" - Maria, Top Affiliate</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact/Footer Section */}
      <footer id="contact" className="bg-gray-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Music className="h-8 w-8 text-primary" />
                <span className="text-2xl font-bold">KMStudioPH</span>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                Creating personalized inspirational songs that touch hearts and strengthen faith. 
                Every melody we craft carries a message of hope, love, and divine blessing.
              </p>
              <div className="flex space-x-4">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => window.open('https://www.facebook.com/kmstudiophofficial/', '_blank')}
                  className="text-gray-400 hover:text-white"
                  aria-label="Visit KMStudioPH official Facebook page"
                >
                  <Facebook className="h-6 w-6" />
                </Button>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li><button onClick={() => scrollToSection('home')} className="text-gray-300 hover:text-white transition-colors">Home</button></li>
                <li><button onClick={() => scrollToSection('songs')} className="text-gray-300 hover:text-white transition-colors">Our Songs</button></li>
                <li><button onClick={() => scrollToSection('order')} className="text-gray-300 hover:text-white transition-colors">Order Custom Song</button></li>
                <li><button onClick={() => scrollToSection('pricing')} className="text-gray-300 hover:text-white transition-colors">Pricing</button></li>
              </ul>
            </div>
            

          </div>
          
          <div className="border-t border-gray-700 mt-12 pt-8 text-center">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 mb-2 md:mb-0">
                &copy; 2025 KMStudioPH. All rights reserved. | Creating music with faith and love.
              </p>
              <div className="flex space-x-4 text-sm">
                <Link href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">
                  Privacy Policy & Terms
                </Link>
                <span className="text-gray-500">|</span>
                <span className="text-gray-400">Made with ❤️ in the Philippines</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
      {/* Payment Modal */}
      <Dialog open={paymentModalOpen} onOpenChange={setPaymentModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Payment Options</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-gray-600">
              Choose your preferred payment method for the <span className="font-semibold capitalize">{selectedPackage}</span> package 
              ({packages[selectedPackage as keyof typeof packages]?.price})
            </p>
            
            <div className="space-y-3 md:space-y-4">
              {/* PayPal Payment */}
              <div className="border rounded-lg p-3 md:p-4 bg-blue-50 hover-lift transition-all duration-300 animate-fade-in-up">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center text-sm md:text-base">
                  <CreditCard className="mr-2 h-4 w-4 md:h-5 md:w-5 text-blue-600" />
                  PayPal (International Cards)
                </h4>
                <PayPalButton 
                  amount={selectedPackage === 'basic' ? '1000' : selectedPackage === 'standard' ? '2000' : '3500'}
                  currency="PHP"
                  intent="capture"
                />
              </div>
              
              {/* GCash Payment */}
              <div className="border rounded-lg p-3 md:p-4 bg-green-50 hover-lift transition-all duration-300 animate-fade-in-up animate-delay-100">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center text-sm md:text-base">
                  <QrCode className="mr-2 h-4 w-4 md:h-5 md:w-5 text-green-600" />
                  GCash
                </h4>
                <Button 
                  onClick={showGCashQR}
                  className="w-full bg-green-600 hover:bg-green-700 text-white mobile-btn hover-lift"
                >
                  Pay with GCash QR Code
                </Button>
                <p className="text-xs md:text-sm text-green-700 mt-2">Send to GCash: 0917-578-1803 (KMStudioPH)</p>
              </div>
              
              {/* Maya Payment */}
              <div className="border rounded-lg p-3 md:p-4 bg-purple-50 hover-lift transition-all duration-300 animate-fade-in-up animate-delay-200">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center text-sm md:text-base">
                  <CreditCard className="mr-2 h-4 w-4 md:h-5 md:w-5 text-purple-600" />
                  Maya (PayMaya)
                </h4>
                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white mobile-btn hover-lift"
                  onClick={() => alert('Send payment to Maya: 0917-578-1803 (KMStudioPH). Please message us with payment proof.')}
                >
                  Pay with Maya
                </Button>
                <p className="text-xs md:text-sm text-purple-700 mt-2">Send to Maya: 0917-578-1803 (KMStudioPH)</p>
              </div>
              
              {/* Bank Transfer / Credit Card */}
              <div className="border rounded-lg p-3 md:p-4 bg-gray-50 hover-lift transition-all duration-300 animate-fade-in-up animate-delay-300">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center text-sm md:text-base">
                  <CreditCard className="mr-2 h-4 w-4 md:h-5 md:w-5 text-gray-600" />
                  Bank Transfer
                </h4>
                <div className="space-y-2 text-xs md:text-sm text-gray-700 mb-3">
                  <p><strong>BPI:</strong> XXXX-XXXX-XX (KMStudioPH)</p>
                  <p><strong>BDO:</strong> XXXX-XXXX-XX (KMStudioPH)</p>
                  <p><strong>Metrobank:</strong> XXXX-XXXX-XX (KMStudioPH)</p>
                </div>
                <Button 
                  variant="outline" 
                  className="w-full mobile-btn hover-lift"
                  onClick={() => alert('After bank transfer, please send payment proof via Facebook message or email.')}
                >
                  Bank Transfer Instructions
                </Button>
              </div>
            </div>
            
            <p className="text-sm text-gray-500 text-center">
              After payment, we'll contact you within 24 hours to begin your custom song creation.
            </p>
          </div>
        </DialogContent>
      </Dialog>
      {/* GCash QR Modal */}
      <Dialog open={gcashModalOpen} onOpenChange={setGcashModalOpen}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>GCash Payment</DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4">
            <div className="bg-gray-100 rounded-xl p-8">
              <QrCode className="h-24 w-24 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-500 mt-2">QR Code will appear here</p>
            </div>
            
            <p className="text-gray-600">Scan this QR code with your GCash app</p>
            <p className="text-sm text-gray-500">
              Reference: <span className="font-mono bg-gray-100 px-2 py-1 rounded">KMSONG-{Math.floor(Math.random() * 100000)}</span>
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
