import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Home from "@/pages/home";
import GiftASong from "@/pages/gift-a-song";
import Feedback from "@/pages/feedback";
import ReferralGraphics from "@/pages/referral-graphics";
import AffiliateDashboard from "@/pages/affiliate-dashboard";
import SalesDashboard from "@/pages/sales-dashboard";
import PrivacyPolicy from "@/pages/privacy-policy";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/gift-a-song" component={GiftASong} />
      <Route path="/feedback" component={Feedback} />
      <Route path="/referral-graphics" component={ReferralGraphics} />
      <Route path="/affiliate-dashboard" component={AffiliateDashboard} />
      <Route path="/sales-dashboard" component={SalesDashboard} />
      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
