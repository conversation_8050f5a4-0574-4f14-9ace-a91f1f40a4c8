# KMStudioPH - Custom Song Creation Platform

A professional one-page website for KMStudioPH music brand offering custom song ordering with comprehensive payment solutions and engaging user experience.

## 🎵 Features

### Core Functionality
- **Custom Song Ordering**: Professional order form with detailed requirements
- **Multiple Payment Methods**: PayPal, GCash, Maya (PayMaya), and Bank Transfer
- **Responsive Design**: Mobile-first approach with smooth transitions
- **Real-time Order Management**: Google Sheets integration for order tracking
- **Professional Branding**: Purple, pink, and blue gradient theme with enhanced visibility
- **Affiliate Program**: 5% commission structure with explosive bonus rewards
- **Dashboard Systems**: Sales and affiliate dashboards with comprehensive tracking

### Payment Options
- **PayPal**: International credit/debit card payments
- **GCash**: 0917-578-1803 (KMStudioPH)
- **Maya**: 0917-578-1803 (KMStudioPH)
- **Bank Transfer**: BPI, BDO, Metrobank options

### Package Pricing
- **Basic Package**: ₱1,000 - Custom lyrics, simple melody, basic recording, MP3 delivery
- **Standard Package**: ₱2,000 - Enhanced features with better production quality
- **Premium Package**: ₱3,500 - Full professional production with premium features

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn package manager

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```
4. Open your browser to `http://localhost:5173`

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for responsive styling
- **Shadcn/ui** components for consistent design
- **React Hook Form** with Zod validation
- **TanStack Query** for data management
- **Wouter** for client-side routing

### Backend
- **Express.js** server
- **TypeScript** for type safety
- **In-memory storage** (MemStorage) for order management
- **CORS enabled** for cross-origin requests

### Payment Integration
- **PayPal SDK** for international payments
- **Manual payment methods** for local Philippine options

### Development Tools
- **ESLint** and **Prettier** for code quality
- **PostCSS** with Tailwind CSS processing
- **Hot Module Replacement** for instant updates

## 📱 Responsive Design

The website is fully responsive with:
- **Mobile-first approach** with breakpoints at 640px, 768px, 1024px
- **Touch-friendly interfaces** with 44px minimum touch targets
- **Smooth animations** with CSS transitions and fade-in effects
- **Accessible navigation** with keyboard support
- **Optimized typography** that scales across devices

## 🎨 Design System

### Color Palette
- **Primary Colors**: Purple, Pink, Blue gradient themes
- **Text Colors**: Dark purple (#6B46C1) and red (#DC2626) for visibility
- **Background**: Light gray (#F9FAFB) with white overlays
- **Accent Colors**: Green (GCash), Purple (Maya), Blue (PayPal)
- **Navigation**: Purple-themed buttons and links

### Typography
- **Responsive font sizing**: 14px base on mobile, 16px on desktop
- **Font weights**: Regular (400), Medium (500), Semibold (600), Bold (700)
- **Line heights**: Optimized for readability across devices

## 🔒 Security Features

- **CORS protection** for API endpoints
- **Input validation** with Zod schemas
- **XSS prevention** through proper data sanitization
- **Secure payment processing** via PayPal integration

## 📊 Order Management

Orders are stored in memory with the following data structure:
```typescript
interface SongOrder {
  id: number;
  songTitle: string;
  artistName: string;
  genre: string;
  mood: string;
  description: string;
  deadline: string;
  package: string;
  customerName: string;
  email: string;
  phone: string;
  createdAt: string;
}
```

## 🌐 Deployment

The application is ready for deployment on Replit with:
- **Automatic builds** via Vite
- **Environment variable support** for configuration
- **Static file serving** for production builds
- **Health check endpoints** for monitoring

## 📞 Contact Information

- **Website**: KMStudioPH
- **GCash/Maya**: 0917-578-1803
- **Services**: Custom song creation, inspirational music, wedding songs

## 🔄 Development Workflow

1. **Local Development**: Use `npm run dev` for hot reloading
2. **Code Quality**: ESLint and Prettier configured
3. **Type Safety**: Full TypeScript coverage
4. **Component Library**: Shadcn/ui for consistent components
5. **Responsive Testing**: Built-in mobile-responsive design

## 🎯 Recent Updates

### UI/UX Improvements
- **Streamlined Experience**: Removed "Gift a Song" and "A Mother's Heart in Melody" sections
- **Enhanced Visibility**: Updated text colors to dark purple and red for better readability
- **Purple Theme**: Navigation buttons and affiliate sections now use consistent purple styling
- **Improved Dashboard**: Moved explosive bonus rewards to top of affiliate dashboard
- **Better Navigation**: Added back-to-home buttons for easier navigation

### Affiliate Program Enhancements
- **Prominent Bonus Display**: Explosive bonus rewards prominently featured
- **Visual Improvements**: White backgrounds with high-contrast text
- **Commission Structure**: Updated to 5% across all package types
- **Dashboard Features**: Comprehensive tracking and withdrawal systems

## 📈 Performance Optimizations

- **Lazy loading** for images and components
- **Optimized bundle size** with Vite
- **Efficient re-renders** with React Query
- **Smooth animations** with CSS transforms
- **Fast navigation** with client-side routing

## 🛡️ Accessibility (WCAG 2.1 AA Compliant)

- **Semantic HTML** structure
- **Keyboard navigation** support
- **Screen reader compatibility**
- **High contrast ratios** for text readability
- **Alt text** for all images
- **ARIA labels** for interactive elements

---

**Built with ❤️ for KMStudioPH - Creating inspirational music that touches hearts and souls.**