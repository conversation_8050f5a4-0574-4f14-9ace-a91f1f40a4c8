import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Calendar, 
  Download,
  CreditCard,
  Smartphone,
  Building2,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowLeft
} from "lucide-react";
import { Link } from "wouter";

const withdrawalSchema = z.object({
  amount: z.number().min(100, "Minimum withdrawal is ₱100"),
  paymentMethod: z.enum(["gcash", "bank", "paypal"]),
  accountDetails: z.string().min(1, "Account details are required"),
  notes: z.string().optional(),
});

type WithdrawalForm = z.infer<typeof withdrawalSchema>;

const AffiliateDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("30");
  const { toast } = useToast();

  // Mock data - in real app, this would come from API
  const affiliateData = {
    name: "Maria Santos",
    code: "REF001MARIA",
    joinDate: "2025-01-15",
    status: "Active",
    totalEarnings: 2350,
    availableBalance: 1850,
    pendingCommissions: 500,
    totalReferrals: 12,
    successfulOrders: 9,
    conversionRate: 75,
    thisMonthEarnings: 850,
    rank: "Silver Affiliate"
  };

  const recentReferrals = [
    { id: 1, customerName: "John D.", package: "Standard", amount: 100, status: "Completed", date: "2025-05-22" },
    { id: 2, customerName: "Sarah L.", package: "Premium", amount: 175, status: "In Progress", date: "2025-05-21" },
    { id: 3, customerName: "Mike R.", package: "Basic", amount: 50, status: "Completed", date: "2025-05-20" },
    { id: 4, customerName: "Lisa K.", package: "Standard", amount: 100, status: "Pending", date: "2025-05-19" },
  ];

  const withdrawalHistory = [
    { id: 1, amount: 500, method: "GCash", status: "Completed", date: "2025-05-15", reference: "WD001" },
    { id: 2, amount: 300, method: "Bank Transfer", status: "Processing", date: "2025-05-23", reference: "WD002" },
  ];

  const form = useForm<WithdrawalForm>({
    resolver: zodResolver(withdrawalSchema),
    defaultValues: {
      amount: 0,
      paymentMethod: "gcash",
      accountDetails: "",
      notes: "",
    },
  });

  const withdrawalMutation = useMutation({
    mutationFn: async (data: WithdrawalForm) => {
      return apiRequest("POST", "/api/withdrawal-request", data);
    },
    onSuccess: () => {
      toast({
        title: "Withdrawal Request Submitted!",
        description: "Your withdrawal request has been submitted and will be processed within 2-3 business days.",
      });
      form.reset();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to submit withdrawal request. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmitWithdrawal = (data: WithdrawalForm) => {
    withdrawalMutation.mutate(data);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-800";
      case "In Progress": return "bg-blue-100 text-blue-800";
      case "Processing": return "bg-yellow-100 text-yellow-800";
      case "Pending": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed": return <CheckCircle className="w-4 h-4" />;
      case "Processing": return <Clock className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center gap-4 mb-2">
                <Link href="/">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    Back to Home
                  </Button>
                </Link>
              </div>
              <h1 className="text-4xl font-bold text-foreground mb-2">
                Welcome back, {affiliateData.name}! 👋
              </h1>
              <p className="text-muted-foreground">
                Affiliate Code: <span className="font-mono bg-muted px-2 py-1 rounded">{affiliateData.code}</span>
              </p>
            </div>
            <Badge variant="secondary" className="text-lg px-4 py-2">
              {affiliateData.rank}
            </Badge>
          </div>
        </div>

        {/* Explosive Bonus Rewards Section */}
        <div className="mb-8">
          <Card className="bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500 border-0 text-white">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">🚀 EXPLOSIVE BONUS REWARDS! 🚀</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold">₱250 BONUS!</div>
                  <div className="text-sm">Hit 5 referrals this month</div>
                  <div className="text-xs opacity-90">💪 YOU CAN DO THIS!</div>
                </div>
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold">₱600 MEGA BONUS!</div>
                  <div className="text-sm">Reach 10 referrals this month</div>
                  <div className="text-xs opacity-90">🔥 ALMOST DOUBLE YOUR EARNINGS!</div>
                </div>
                <div className="bg-yellow-400/30 border border-yellow-300/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-100">₱1,500 CHAMPION BONUS!</div>
                  <div className="text-sm">Be the TOP affiliate this month</div>
                  <div className="text-xs opacity-90">👑 BECOME THE AFFILIATE CHAMPION!</div>
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm font-semibold animate-pulse">
                  ⚡ BONUSES STACK WITH YOUR COMMISSIONS! ⚡
                </div>
                <div className="text-xs mt-1 opacity-90">
                  Earn up to ₱2,100 EXTRA per month in bonuses alone!
                </div>
              </div>
              
              {/* Limited Time Offer */}
              <div className="bg-gradient-to-r from-red-500 to-orange-500 rounded-lg p-4 text-center border-2 border-yellow-400">
                <div className="text-lg font-bold">🔥 LIMITED TIME: DOUBLE BONUS WEEK! 🔥</div>
                <div className="text-sm mt-1">Join this week and get 2X bonus on your first 3 referrals!</div>
                <div className="bg-red-600/80 rounded-lg px-3 py-1 inline-block mt-2">
                  <div className="text-sm font-semibold">⏰ Hurry! Offer ends soon!</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Available Balance</p>
                  <p className="text-2xl font-bold text-green-600">₱{affiliateData.availableBalance.toLocaleString()}</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">This Month</p>
                  <p className="text-2xl font-bold text-primary">₱{affiliateData.thisMonthEarnings.toLocaleString()}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Referrals</p>
                  <p className="text-2xl font-bold text-blue-600">{affiliateData.totalReferrals}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Conversion Rate</p>
                  <p className="text-2xl font-bold text-purple-600">{affiliateData.conversionRate}%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recent Referrals */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Recent Referrals</CardTitle>
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentReferrals.map((referral) => (
                    <div key={referral.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <Users className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{referral.customerName}</p>
                          <p className="text-sm text-muted-foreground">{referral.package} Package • {referral.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">₱{referral.amount}</p>
                        <Badge className={getStatusColor(referral.status)}>
                          {getStatusIcon(referral.status)}
                          <span className="ml-1">{referral.status}</span>
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Withdrawal History */}
            <Card>
              <CardHeader>
                <CardTitle>Withdrawal History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {withdrawalHistory.map((withdrawal) => (
                    <div key={withdrawal.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <Download className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">₱{withdrawal.amount.toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">{withdrawal.method} • {withdrawal.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">#{withdrawal.reference}</p>
                        <Badge className={getStatusColor(withdrawal.status)}>
                          {getStatusIcon(withdrawal.status)}
                          <span className="ml-1">{withdrawal.status}</span>
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full" size="lg">
                      <CreditCard className="w-5 h-5 mr-2" />
                      Request Withdrawal
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Request Withdrawal</DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmitWithdrawal)} className="space-y-4">
                        <FormField
                          control={form.control}
                          name="amount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Amount (₱)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="Enter amount"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-xs text-muted-foreground">
                                Available: ₱{affiliateData.availableBalance.toLocaleString()}
                              </p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="paymentMethod"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Payment Method</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select payment method" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="gcash">
                                    <div className="flex items-center">
                                      <Smartphone className="w-4 h-4 mr-2" />
                                      GCash
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="bank">
                                    <div className="flex items-center">
                                      <Building2 className="w-4 h-4 mr-2" />
                                      Bank Transfer
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="paypal">
                                    <div className="flex items-center">
                                      <CreditCard className="w-4 h-4 mr-2" />
                                      PayPal
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="accountDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Account Details</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Account number or email"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Notes (Optional)</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Additional notes..."
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button 
                          type="submit" 
                          className="w-full" 
                          disabled={withdrawalMutation.isPending}
                        >
                          {withdrawalMutation.isPending ? "Submitting..." : "Submit Request"}
                        </Button>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>

                <Button variant="outline" className="w-full">
                  <Eye className="w-5 h-5 mr-2" />
                  View Analytics
                </Button>

                <Button variant="outline" className="w-full">
                  <Download className="w-5 h-5 mr-2" />
                  Download Report
                </Button>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Earnings:</span>
                  <span className="font-bold">₱{affiliateData.totalEarnings.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Pending:</span>
                  <span className="font-bold text-yellow-600">₱{affiliateData.pendingCommissions.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Successful Orders:</span>
                  <span className="font-bold">{affiliateData.successfulOrders}/{affiliateData.totalReferrals}</span>
                </div>
                <Separator />
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Member since</p>
                  <p className="font-semibold">{new Date(affiliateData.joinDate).toLocaleDateString()}</p>
                </div>
              </CardContent>
            </Card>

            {/* Next Level Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Level Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Silver Affiliate</span>
                    <span>12/20 referrals</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    8 more referrals to reach Gold level and unlock higher commissions!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AffiliateDashboard;