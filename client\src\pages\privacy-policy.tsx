import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Shield, Lock, Eye, FileText } from "lucide-react";
import { <PERSON> } from "wouter";

const PrivacyPolicy = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-teal-800">
      <div className="max-w-4xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="mb-6 text-white border-white/30 hover:bg-white/10">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Privacy Policy & Terms of Service
            </h1>
            <p className="text-blue-100">
              Your privacy and trust matter to us at KMStudioPH
            </p>
            <p className="text-sm text-blue-200 mt-2">
              Last updated: May 25, 2025
            </p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Privacy Policy */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <Shield className="w-6 h-6 mr-3 text-blue-300" />
                Privacy Policy
              </CardTitle>
            </CardHeader>
            <CardContent className="text-white/90 space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-blue-200 mb-2">Information We Collect</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Personal information: Name, email address, phone number</li>
                  <li>• Payment information: Payment method preferences (processed securely)</li>
                  <li>• Song details: Custom song requests, lyrics, and preferences</li>
                  <li>• Communication records: Messages, feedback, and support interactions</li>
                  <li>• Affiliate information: Referral codes and commission tracking</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-blue-200 mb-2">How We Use Your Information</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Create and deliver your custom songs</li>
                  <li>• Process payments and manage your orders</li>
                  <li>• Communicate about your projects and updates</li>
                  <li>• Improve our services and customer experience</li>
                  <li>• Manage affiliate programs and commissions</li>
                  <li>• Send important service notifications</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-blue-200 mb-2">Information Protection</h3>
                <ul className="space-y-2 text-sm">
                  <li>• We use industry-standard security measures</li>
                  <li>• Payment information is processed through secure gateways</li>
                  <li>• Personal data is stored securely and access is limited</li>
                  <li>• We never sell your personal information to third parties</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Terms of Service */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <FileText className="w-6 h-6 mr-3 text-purple-300" />
                Terms of Service
              </CardTitle>
            </CardHeader>
            <CardContent className="text-white/90 space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-purple-200 mb-2">Service Description</h3>
                <p className="text-sm">
                  KMStudioPH provides custom song creation services using AI-assisted composition with human creativity and oversight. 
                  Our songs are professionally crafted with human-written lyrics and AI-enhanced musical arrangements.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-purple-200 mb-2">Free Trial & Pricing</h3>
                <ul className="space-y-2 text-sm">
                  <li>• First 2 custom songs are FREE for new customers</li>
                  <li>• Additional songs follow our standard pricing structure</li>
                  <li>• All prices are in Philippine Peso (₱)</li>
                  <li>• Payment is required before song production begins</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-purple-200 mb-2">Delivery & Turnaround</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Basic Package: 3-day turnaround</li>
                  <li>• Standard Package: 5-7 day turnaround</li>
                  <li>• Premium Package: 7-10 day turnaround</li>
                  <li>• Rush orders may be available for additional fees</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-purple-200 mb-2">Intellectual Property</h3>
                <ul className="space-y-2 text-sm">
                  <li>• You own the rights to your custom song upon full payment</li>
                  <li>• Songs are created uniquely for each customer</li>
                  <li>• We retain the right to use anonymized work samples for marketing</li>
                  <li>• Commercial use rights included with Premium packages</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-purple-200 mb-2">Affiliate Program</h3>
                <ul className="space-y-2 text-sm">
                  <li>• 5% commission on successful referrals</li>
                  <li>• Commissions paid monthly via GCash or bank transfer</li>
                  <li>• Minimum payout threshold: ₱500</li>
                  <li>• Bonus rewards for high-performing affiliates</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Contact & Rights */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <Eye className="w-6 h-6 mr-3 text-teal-300" />
                Your Rights & Contact
              </CardTitle>
            </CardHeader>
            <CardContent className="text-white/90 space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-teal-200 mb-2">Your Data Rights</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Request access to your personal information</li>
                  <li>• Request corrections to inaccurate data</li>
                  <li>• Request deletion of your personal information</li>
                  <li>• Opt-out of marketing communications</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-teal-200 mb-2">Contact Us</h3>
                <div className="text-sm space-y-1">
                  <p>📱 GCash/Maya: 0917-578-1803</p>
                  <p>📘 Facebook: KMStudioPH</p>
                  <p>📧 Email: Through our website contact form</p>
                </div>
              </div>

              <Separator className="bg-white/20" />

              <div className="text-center">
                <p className="text-sm text-blue-200">
                  By using our services, you agree to these terms and our privacy policy.
                </p>
                <p className="text-xs text-blue-300 mt-2">
                  KMStudioPH - Creating Musical Memories Since 2024
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link href="/">
            <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white">
              Return to KMStudioPH
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;